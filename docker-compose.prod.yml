version: '3.8'

services:
  # Django Web Application (Production with SQLite)
  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 --workers 3 --timeout 120 --keep-alive 2 info.wsgi:application
    volumes:
      - ./info:/app/info
      - media_volume:/app/media
      - static_volume:/app/staticfiles
      - db_volume:/app/info # SQLite database volume (maps to Django project directory)
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=production-secret-key-change-this-in-deployment-$(date +%s)
      - ALLOWED_HOSTS=*
      - DJANGO_SETTINGS_MODULE=info.settings
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - default

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - static_volume:/app/staticfiles:ro
      - media_volume:/app/media:ro
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - default

volumes:
  media_volume:
  static_volume:
  db_volume:
    # Persistent SQLite database storage

networks:
  default:
    driver: bridge
