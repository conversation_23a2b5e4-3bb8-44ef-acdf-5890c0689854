
from django.shortcuts import render, redirect
from .models import Announcement
from django.core.mail import send_mail
from .forms import EmailForm
from django.contrib import messages

def home(request):
    return render(request, 'info_board/home.html')

def announcement_list(request):
    announcements = Announcement.objects.filter(is_active=True).order_by('-date_posted')
    return render(request, 'info_board/announcement_list.html', {'announcements': announcements})


def send_email(request):
    if request.method == 'POST':
        form = EmailForm(request.POST)
        if form.is_valid():
            sender_name = form.cleaned_data['sender_name']
            content = f"Küld<PERSON> neve: {sender_name}\n\n{form.cleaned_data['content']}"
            send_mail(
                'Információs portál',  # Statikus tárgy
                content,
                form.cleaned_data['sender_email'],
                [form.cleaned_data['recipient_email']],
                fail_silently=False,
            )
            messages.success(request, 'Email si<PERSON>esen el<PERSON>ld<PERSON>!')
            return redirect('send_email')
    else:
        form = EmailForm()
    return render(request, 'info_board/email_form.html', {'form': form})