
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse
from .models import Announcement
from django.core.mail import send_mail
from .forms import EmailForm
from django.contrib import messages
import urllib.parse


def home(request):
    return render(request, 'info_board/home.html')


def announcement_list(request):
    announcements = Announcement.objects.filter(
        is_active=True).order_by('-date_posted')
    return render(request, 'info_board/announcement_list.html', {'announcements': announcements})


def powerpoint_embed(request, announcement_id):
    """Generate embedded PowerPoint viewer URL for Microsoft Office Online."""
    announcement = get_object_or_404(
        Announcement, id=announcement_id, is_active=True)

    if not announcement.has_powerpoint_attachment():
        return HttpResponse("No PowerPoint attachment found", status=404)

    # Build the full URL to the PowerPoint file
    file_url = request.build_absolute_uri(announcement.attachment_file.url)

    # Encode the URL for Microsoft Office Online viewer
    encoded_url = urllib.parse.quote(file_url, safe='')

    # Microsoft Office Online PowerPoint viewer URL
    office_viewer_url = f"https://view.officeapps.live.com/op/embed.aspx?src={encoded_url}"

    return render(request, 'info_board/powerpoint_embed.html', {
        'announcement': announcement,
        'office_viewer_url': office_viewer_url
    })


def send_email(request):
    if request.method == 'POST':
        form = EmailForm(request.POST)
        if form.is_valid():
            sender_name = form.cleaned_data['sender_name']
            content = f"Küldő neve: {sender_name}\n\n{form.cleaned_data['content']}"
            send_mail(
                'Információs portál',  # Statikus tárgy
                content,
                form.cleaned_data['sender_email'],
                [form.cleaned_data['recipient_email']],
                fail_silently=False,
            )
            messages.success(request, 'Email sikeresen elküldve!')
            return redirect('send_email')
    else:
        form = EmailForm()
    return render(request, 'info_board/email_form.html', {'form': form})
