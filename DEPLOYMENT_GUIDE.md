# InfoTable Deployment Guide

This guide explains how to deploy the InfoTable application to another PC and run it as a Windows service that starts automatically without requiring user login.

## 📋 Prerequisites

### Target PC Requirements:
- Windows 10/11 or Windows Server 2016+
- Docker Desktop for Windows
- Administrator privileges
- Internet connection (for initial setup)

## 📦 Deployment Package

Copy the entire project folder to the target PC. The deployment package includes:

```
InfoTable/
├── install-service.bat      # Service installer (run as admin)
├── uninstall-service.bat    # Service uninstaller (run as admin)
├── start-app.bat           # Application startup script
├── stop-app.bat            # Application stop script
├── docker-compose.prod.yml  # Production Docker configuration
├── Dockerfile              # Docker image definition
├── entrypoint.sh           # Container startup script
├── nginx.conf              # Nginx configuration
├── requirements.txt        # Python dependencies
├── info/                   # Django application
└── logs/                   # Service logs (created automatically)
```

## 🚀 Installation Steps

### 1. Install Docker Desktop

1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop/
2. Install Docker Desktop
3. Start Docker Desktop and wait for it to be ready
4. **Right-click** on `setup-docker.bat` and select **"Run as administrator"**
   - This configures Docker to start automatically with Windows
   - Restart the computer after running this script

### 2. Copy Application Files

1. Copy the entire InfoTable folder to the target PC
2. Recommended location: `C:\InfoTable\`

### 3. Install as Windows Service

1. **Right-click** on `install-service.bat`
2. Select **"Run as administrator"**
3. Follow the installation prompts
4. The script will:
   - Download NSSM (Non-Sucking Service Manager)
   - Create the Windows service
   - Configure auto-start
   - Start the service

### 4. Verify Installation

1. Open **Services** (`services.msc`)
2. Look for **"InfoTable Application"**
3. Verify it's running and set to **"Automatic"** startup
4. Check logs in the `logs/` folder

## 🌐 Accessing the Application

### Find the Server IP Address:
```cmd
ipconfig
```
Look for the IPv4 address (e.g., *************)

### Access URLs:
- **Local access:** http://localhost or http://127.0.0.1
- **Network access:** http://YOUR_IP_ADDRESS (e.g., http://*************)

## 👤 Create Admin User

After installation, create an admin user:

1. Open Command Prompt as Administrator
2. Navigate to the InfoTable folder
3. Run:
```cmd
docker-compose -f docker-compose.prod.yml exec web python manage.py createsuperuser
```
4. Follow the prompts to create username, email, and password

## 🔧 Service Management

### Check Service Status:
```cmd
sc query InfoTableApp
```

### Start Service Manually:
```cmd
net start InfoTableApp
```

### Stop Service Manually:
```cmd
net stop InfoTableApp
```

### View Service Logs:
Check files in the `logs/` folder:
- `service.log` - Application output
- `service-error.log` - Error messages

## 🛠️ Manual Control (Alternative)

If you prefer manual control instead of service:

### Start Application:
```cmd
start-app.bat
```

### Stop Application:
```cmd
stop-app.bat
```

## 🔒 Security Considerations

### Production Security:
1. **Change Secret Key:** Edit `docker-compose.prod.yml` and change the SECRET_KEY
2. **Firewall:** Configure Windows Firewall to allow port 80
3. **Admin Password:** Use a strong admin password
4. **Network Access:** Consider restricting ALLOWED_HOSTS for specific IPs

### Example Secret Key Change:
```yaml
environment:
  - SECRET_KEY=your-very-long-random-secret-key-here-change-this
```

## 🚨 Troubleshooting

### Service Won't Start:
1. Check Docker Desktop is running
2. Check logs in `logs/service-error.log`
3. Verify Docker containers: `docker ps`
4. Restart Docker Desktop

### Can't Access from Network:
1. Check Windows Firewall settings
2. Verify IP address with `ipconfig`
3. Test locally first: http://localhost
4. Check router/network settings

### Application Errors:
1. Check container logs: `docker-compose -f docker-compose.prod.yml logs`
2. Restart service: `net stop InfoTableApp && net start InfoTableApp`
3. Check disk space and permissions

### Complete Reset:
```cmd
# Stop service
net stop InfoTableApp

# Remove containers and data
docker-compose -f docker-compose.prod.yml down -v

# Start service again
net start InfoTableApp
```

## 📊 Monitoring

### Check Application Status:
```cmd
docker-compose -f docker-compose.prod.yml ps
```

### View Application Logs:
```cmd
docker-compose -f docker-compose.prod.yml logs -f
```

### System Resources:
```cmd
docker stats
```

## 🔄 Updates

To update the application:

1. Stop the service: `net stop InfoTableApp`
2. Replace application files
3. Rebuild: `docker-compose -f docker-compose.prod.yml build`
4. Start the service: `net start InfoTableApp`

## 📞 Support

For issues:
1. Check the logs in `logs/` folder
2. Verify Docker Desktop is running
3. Check Windows Event Viewer for service errors
4. Ensure all files are in the correct location
