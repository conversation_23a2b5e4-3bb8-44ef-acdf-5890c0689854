from django.db import models
from django.db.models.signals import pre_delete, post_delete, pre_save
from django.dispatch import receiver
import os


class Announcement(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    date_posted = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    background_image = models.ImageField(
        upload_to='announcements/', null=True, blank=True)

    def __str__(self):
        return self.title


class EmailMessage(models.Model):
    sender_email = models.EmailField()
    recipient_email = models.EmailField()
    content = models.TextField()


# Signal handlers for automatic image deletion
@receiver(pre_delete, sender=Announcement)
def delete_announcement_image(sender, instance, **kwargs):
    """
    Delete the associated image file when an Announcement is deleted.
    This signal is triggered before the model instance is deleted from the database.
    """
    if instance.background_image:
        # Check if the file exists and delete it
        if os.path.isfile(instance.background_image.path):
            try:
                os.remove(instance.background_image.path)
                print(f"Deleted image file: {instance.background_image.path}")
            except OSError as e:
                print(
                    f"Error deleting image file {instance.background_image.path}: {e}")


@receiver(post_delete, sender=Announcement)
def cleanup_empty_directories(sender, instance, **kwargs):
    """
    Clean up empty directories after deleting an announcement.
    This helps keep the media directory organized.
    """
    if instance.background_image:
        try:
            # Get the directory path
            directory = os.path.dirname(instance.background_image.path)
            # Remove directory if it's empty
            if os.path.exists(directory) and not os.listdir(directory):
                os.rmdir(directory)
                print(f"Removed empty directory: {directory}")
        except OSError as e:
            print(f"Error removing directory: {e}")


@receiver(pre_save, sender=Announcement)
def delete_old_image_on_update(sender, instance, **kwargs):
    """
    Delete the old image file when an Announcement's image is updated.
    This prevents orphaned image files when images are replaced.
    """
    if not instance.pk:
        # This is a new instance, no old image to delete
        return

    try:
        # Get the old instance from the database
        old_instance = Announcement.objects.get(pk=instance.pk)

        # Check if the image field has changed
        if old_instance.background_image and old_instance.background_image != instance.background_image:
            # Delete the old image file
            if os.path.isfile(old_instance.background_image.path):
                try:
                    os.remove(old_instance.background_image.path)
                    print(
                        f"Deleted old image file: {old_instance.background_image.path}")
                except OSError as e:
                    print(
                        f"Error deleting old image file {old_instance.background_image.path}: {e}")
    except Announcement.DoesNotExist:
        # Old instance doesn't exist, this is a new record
        pass
