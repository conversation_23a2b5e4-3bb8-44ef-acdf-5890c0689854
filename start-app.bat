@echo off
REM InfoTable Application Startup Script
REM This script starts the InfoTable application using Docker Compose

echo [%date% %time%] Starting InfoTable Application...

REM Set the working directory to the script location
cd /d "%~dp0"

REM Set environment variables
set COMPOSE_PROJECT_NAME=infotable

REM Wait for Docker Desktop to be ready
echo [%date% %time%] Waiting for Docker to be ready...
:wait_docker
docker info >nul 2>&1
if %errorLevel% neq 0 (
    echo [%date% %time%] Docker not ready, waiting 10 seconds...
    timeout /t 10 /nobreak >nul
    goto wait_docker
)

echo [%date% %time%] Docker is ready!

REM Stop any existing containers
echo [%date% %time%] Stopping any existing containers...
docker-compose -f docker-compose.prod.yml down

REM Start the application
echo [%date% %time%] Starting InfoTable application...
docker-compose -f docker-compose.prod.yml up --build

REM If we reach here, the application has stopped
echo [%date% %time%] InfoTable application has stopped.
