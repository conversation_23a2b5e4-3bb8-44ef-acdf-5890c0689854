{% extends 'info_board/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <button class="btn btn-outline-light back-button mb-3" onclick="history.back()">
                <i class="fas fa-arrow-left"></i> Vissza
            </button>
            <h2 class="mb-3 text-white">{{ announcement.title }}</h2>
            <p class="mb-4 text-white-50">Közzétéve: {{ announcement.date_posted|date:'Y. F j.' }}</p>

            <!-- File Information Card -->
            <div class="file-info-card mb-4">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fas fa-file-powerpoint fa-3x text-warning"></i>
                            </div>
                            <div class="col">
                                <h5 class="card-title mb-1">{{ file_name }}</h5>
                                <p class="card-text mb-1">
                                    <small class="text-muted">
                                        Méret: {{ file_size_mb }} MB | Típus: {{ file_extension|upper }}
                                    </small>
                                </p>
                                <div class="btn-group" role="group">
                                    <a href="{{ file_url }}"
                                       class="btn btn-primary"
                                       download>
                                        <i class="fas fa-download"></i> PowerPoint letöltése
                                    </a>
                                    {% if pdf_url %}
                                        <a href="{{ pdf_url }}"
                                           class="btn btn-success"
                                           download>
                                            <i class="fas fa-file-pdf"></i> PDF letöltése
                                        </a>
                                        <a href="{{ pdf_url }}"
                                           class="btn btn-info"
                                           target="_blank">
                                            <i class="fas fa-external-link-alt"></i> PDF új ablakban
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PDF Viewer or Content -->
            {% if conversion_status == "success" and pdf_url %}
                <!-- PDF Success Card -->
                <div class="pdf-success-card">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">
                                <i class="fas fa-check-circle fa-2x mb-3"></i><br>
                                PowerPoint sikeresen konvertálva PDF-be!
                            </h4>
                            <p class="card-text mb-4">
                                A PowerPoint prezentáció PDF formátumban megtekinthető. Válasszon az alábbi lehetőségek közül:
                            </p>
                            <div class="btn-group-vertical">
                                <a href="{{ pdf_url }}" class="btn btn-light btn-lg mb-3" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> PDF megnyitása új ablakban
                                </a>
                                <button class="btn btn-outline-light btn-lg mb-3" onclick="showEmbeddedPDF()">
                                    <i class="fas fa-window-maximize"></i> Beágyazott megtekintés
                                </button>
                                <a href="{{ pdf_url }}" class="btn btn-outline-light btn-lg" download>
                                    <i class="fas fa-download"></i> PDF letöltése
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Embedded PDF Viewer (hidden by default) -->
                <div id="embedded-pdf-viewer" class="pdf-viewer-container" style="display: none;">
                    <div class="viewer-header">
                        <span class="viewer-title">
                            <i class="fas fa-file-pdf"></i> PDF Megtekintő
                        </span>
                        <div class="viewer-controls">
                            <button class="btn btn-sm btn-outline-light" onclick="hideEmbeddedPDF()">
                                <i class="fas fa-times"></i> Bezárás
                            </button>
                            <button class="btn btn-sm btn-outline-light" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i> Teljes képernyő
                            </button>
                        </div>
                    </div>
                    <div class="pdf-viewer-wrapper">
                        <iframe id="pdf-iframe"
                                src="{{ pdf_url }}"
                                width="100%"
                                height="700px"
                                frameborder="0"
                                style="border: none;">
                            <div class="alert alert-warning">
                                <p>A böngésző nem támogatja a beágyazott PDF megtekintést.</p>
                                <a href="{{ pdf_url }}" class="btn btn-primary" target="_blank">
                                    PDF megnyitása új ablakban
                                </a>
                            </div>
                        </iframe>
                    </div>
                </div>
            {% elif conversion_status == "failed" %}
                <!-- Conversion Failed -->
                <div class="conversion-error">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> Konvertálási hiba</h5>
                        <p>A PowerPoint fájl nem konvertálható PDF formátumba. Kérjük, töltse le az eredeti fájlt.</p>
                        <a href="{{ file_url }}" class="btn btn-primary" download>
                            <i class="fas fa-download"></i> PowerPoint letöltése
                        </a>
                    </div>
                </div>
            {% endif %}

            <!-- Announcement Content -->
            <div class="content-card mt-4">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <h5 class="card-title">Tartalom</h5>
                        <div class="content">
                            {{ announcement.content|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.file-info-card {
    animation: slideUp 0.6s ease-out;
}

.content-card {
    animation: slideUp 0.8s ease-out;
}

.pdf-success-card {
    animation: slideUp 0.6s ease-out;
    margin-bottom: 20px;
}

.pdf-success-card .card {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.pdf-success-card .btn-group-vertical .btn {
    margin-bottom: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pdf-success-card .btn-group-vertical .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pdf-viewer-container {
    background: rgba(51, 51, 51, 0.9);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    animation: slideUp 1s ease-out;
    margin-bottom: 20px;
}

.viewer-header {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.8), rgba(255, 87, 34, 0.8));
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.viewer-title {
    color: white;
    font-weight: bold;
    font-size: 1.1em;
}

.viewer-controls {
    display: flex;
    gap: 10px;
}

.pdf-viewer-wrapper {
    position: relative;
    background: white;
}

.pdf-fallback {
    padding: 20px;
    text-align: center;
}

.conversion-error {
    animation: slideUp 0.8s ease-out;
}

.content {
    line-height: 1.6;
    font-size: 1.1em;
    white-space: pre-wrap;
}

.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fullscreen styles */
.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
}

.fullscreen .pdf-viewer-wrapper iframe {
    height: calc(100vh - 60px) !important;
}

@media (max-width: 768px) {
    .pdf-viewer-container iframe {
        height: 500px !important;
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .btn-group .btn {
        margin-right: 0;
        width: 100%;
    }

    .viewer-header {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .viewer-controls {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .pdf-viewer-container iframe {
        height: 400px !important;
    }

    .file-info-card .row {
        text-align: center;
    }

    .file-info-card .col-auto {
        margin-bottom: 10px;
    }

    .viewer-title {
        font-size: 1em;
    }
}
</style>

<script>
function toggleFullscreen() {
    const container = document.querySelector('.pdf-viewer-container');
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (container.classList.contains('fullscreen')) {
        // Exit fullscreen
        container.classList.remove('fullscreen');
        icon.className = 'fas fa-expand';
        button.innerHTML = '<i class="fas fa-expand"></i> Teljes képernyő';

        // Exit browser fullscreen if supported
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    } else {
        // Enter fullscreen
        container.classList.add('fullscreen');
        icon.className = 'fas fa-compress';
        button.innerHTML = '<i class="fas fa-compress"></i> Kilépés';

        // Request browser fullscreen if supported
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }
    }
}

function showEmbeddedPDF() {
    const successCard = document.querySelector('.pdf-success-card');
    const embeddedViewer = document.getElementById('embedded-pdf-viewer');

    // Hide success card and show embedded viewer
    successCard.style.display = 'none';
    embeddedViewer.style.display = 'block';

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function hideEmbeddedPDF() {
    const successCard = document.querySelector('.pdf-success-card');
    const embeddedViewer = document.getElementById('embedded-pdf-viewer');

    // Hide embedded viewer and show success card
    embeddedViewer.style.display = 'none';
    successCard.style.display = 'block';

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Handle escape key to exit fullscreen
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const container = document.querySelector('.pdf-viewer-container');
        if (container && container.classList.contains('fullscreen')) {
            toggleFullscreen();
        }
    }
});

// Handle fullscreen change events
document.addEventListener('fullscreenchange', handleFullscreenChange);
document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
document.addEventListener('mozfullscreenchange', handleFullscreenChange);
document.addEventListener('MSFullscreenChange', handleFullscreenChange);

function handleFullscreenChange() {
    const container = document.querySelector('.pdf-viewer-container');
    const button = document.querySelector('.viewer-controls button');

    if (!document.fullscreenElement && !document.webkitFullscreenElement &&
        !document.mozFullScreenElement && !document.msFullscreenElement) {
        // Exited fullscreen
        if (container && container.classList.contains('fullscreen')) {
            container.classList.remove('fullscreen');
            if (button) {
                button.innerHTML = '<i class="fas fa-expand"></i> Teljes képernyő';
            }
        }
    }
}
</script>
{% endblock %}
