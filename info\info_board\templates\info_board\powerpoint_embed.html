{% extends 'info_board/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <button class="btn btn-outline-light back-button mb-3" onclick="history.back()">
                <i class="fas fa-arrow-left"></i> Vissza
            </button>
            <h2 class="mb-3 text-white">{{ announcement.title }}</h2>
            <p class="mb-4 text-white-50">Közzétéve: {{ announcement.date_posted|date:'Y. F j.' }}</p>

            <!-- File Information Card -->
            <div class="file-info-card mb-4">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fas fa-file-powerpoint fa-3x text-warning"></i>
                            </div>
                            <div class="col">
                                <h5 class="card-title mb-1">{{ file_name }}</h5>
                                <p class="card-text mb-1">
                                    <small class="text-muted">
                                        Méret: {{ file_size_mb }} MB | Típus: {{ file_extension|upper }}
                                    </small>
                                </p>
                                <div class="btn-group" role="group">
                                    <a href="{{ file_url }}"
                                       class="btn btn-primary"
                                       download>
                                        <i class="fas fa-download"></i> Letöltés
                                    </a>
                                    <button class="btn btn-outline-light" onclick="tryGoogleViewer()">
                                        <i class="fas fa-eye"></i> Megtekintés (Google)
                                    </button>
                                    <button class="btn btn-outline-light" onclick="tryOfficeViewer()">
                                        <i class="fas fa-eye"></i> Megtekintés (Office)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Display Area -->
            <div class="content-display-area">
                <!-- Default: Show announcement content -->
                <div id="content-view" class="content-card">
                    <div class="card bg-dark text-white">
                        <div class="card-body">
                            <h5 class="card-title">Tartalom</h5>
                            <div class="content">
                                {{ announcement.content|linebreaks }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Viewer Container (hidden by default) -->
                <div id="viewer-container" class="powerpoint-container" style="display: none;">
                    <div class="viewer-header">
                        <button class="btn btn-sm btn-outline-light" onclick="hideViewer()">
                            <i class="fas fa-times"></i> Bezárás
                        </button>
                        <span class="viewer-title">PowerPoint Megtekintő</span>
                    </div>
                    <iframe id="powerpoint-iframe"
                            width="100%"
                            height="600px"
                            frameborder="0"
                            style="border: none; background: white;">
                    </iframe>
                    <div class="viewer-fallback" style="display: none;">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> A megtekintő nem tölthető be</h5>
                            <p>A PowerPoint fájl nem jeleníthető meg beágyazott nézetben. Kérjük, töltse le a fájlt a megtekintéshez.</p>
                            <a href="{{ file_url }}" class="btn btn-primary" download>
                                <i class="fas fa-download"></i> Fájl letöltése
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.file-info-card {
    animation: slideUp 0.6s ease-out;
}

.content-card {
    animation: slideUp 0.8s ease-out;
}

.powerpoint-container {
    background: rgba(51, 51, 51, 0.9);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    animation: slideUp 1s ease-out;
}

.viewer-header {
    background: rgba(0, 123, 255, 0.8);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.viewer-title {
    color: white;
    font-weight: bold;
}

.content {
    line-height: 1.6;
    font-size: 1.1em;
    white-space: pre-wrap;
}

.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .powerpoint-container iframe {
        height: 400px;
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .btn-group .btn {
        margin-right: 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .powerpoint-container iframe {
        height: 300px;
    }

    .file-info-card .row {
        text-align: center;
    }

    .file-info-card .col-auto {
        margin-bottom: 10px;
    }
}
</style>

<script>
const googleViewerUrl = "{{ google_viewer_url|escapejs }}";
const officeViewerUrl = "{{ office_viewer_url|escapejs }}";

function tryGoogleViewer() {
    showViewer(googleViewerUrl, "Google Docs Viewer");
}

function tryOfficeViewer() {
    showViewer(officeViewerUrl, "Microsoft Office Online");
}

function showViewer(url, viewerName) {
    const contentView = document.getElementById('content-view');
    const viewerContainer = document.getElementById('viewer-container');
    const iframe = document.getElementById('powerpoint-iframe');
    const viewerTitle = document.querySelector('.viewer-title');
    const fallback = document.querySelector('.viewer-fallback');

    // Hide content view
    contentView.style.display = 'none';

    // Show viewer container
    viewerContainer.style.display = 'block';
    viewerTitle.textContent = viewerName;

    // Set iframe source
    iframe.src = url;

    // Set up error handling
    iframe.onload = function() {
        fallback.style.display = 'none';
    };

    iframe.onerror = function() {
        showFallback();
    };

    // Timeout fallback (in case iframe doesn't load properly)
    setTimeout(function() {
        if (iframe.contentDocument === null) {
            showFallback();
        }
    }, 10000); // 10 second timeout
}

function showFallback() {
    const iframe = document.getElementById('powerpoint-iframe');
    const fallback = document.querySelector('.viewer-fallback');

    iframe.style.display = 'none';
    fallback.style.display = 'block';
}

function hideViewer() {
    const contentView = document.getElementById('content-view');
    const viewerContainer = document.getElementById('viewer-container');
    const iframe = document.getElementById('powerpoint-iframe');

    // Hide viewer
    viewerContainer.style.display = 'none';
    iframe.src = '';

    // Show content view
    contentView.style.display = 'block';
}
</script>
{% endblock %}
