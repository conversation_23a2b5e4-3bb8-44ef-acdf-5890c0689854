{% extends 'info_board/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <button class="btn btn-outline-light back-button mb-3" onclick="history.back()">
                <i class="fas fa-arrow-left"></i> Vissza
            </button>
            <h2 class="mb-3 text-white">{{ announcement.title }}</h2>
            <p class="mb-4 text-white-50">Közzétéve: {{ announcement.date_posted|date:'Y. F j.' }}</p>

            <!-- File Information Card -->
            <div class="file-info-card mb-4">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                {% if file_type == "PDF" %}
                                    <i class="fas fa-file-pdf fa-3x text-danger"></i>
                                {% else %}
                                    <i class="fas fa-file-powerpoint fa-3x text-warning"></i>
                                {% endif %}
                            </div>
                            <div class="col">
                                <h5 class="card-title mb-1">{{ file_name }}</h5>
                                <p class="card-text mb-1">
                                    <small class="text-muted">
                                        Méret: {{ file_size_mb }} MB | Típus: {{ file_extension|upper }}
                                    </small>
                                </p>
                                <div class="btn-group" role="group">
                                    <a href="{{ file_url }}"
                                       class="btn btn-primary"
                                       download>
                                        <i class="fas fa-download"></i>
                                        {% if file_type == "PDF" %}PDF{% else %}PowerPoint{% endif %} letöltése
                                    </a>
                                    {% if pdf_url and file_type != "PDF" %}
                                        <a href="{{ pdf_url }}"
                                           class="btn btn-success"
                                           download>
                                            <i class="fas fa-file-pdf"></i> PDF letöltése
                                        </a>
                                    {% endif %}
                                    {% if pdf_url %}
                                        <a href="{{ pdf_url }}"
                                           class="btn btn-info"
                                           target="_blank">
                                            <i class="fas fa-external-link-alt"></i> PDF új ablakban
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PDF Success -->
            {% if conversion_status == "success" and pdf_url %}
                <!-- PowerPoint Converted to PDF Success Card -->
                <div class="pdf-success-card">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">
                                <i class="fas fa-check-circle fa-2x mb-3"></i><br>
                                PowerPoint sikeresen konvertálva PDF-be!
                            </h4>
                            <p class="card-text mb-4">
                                A PowerPoint prezentáció PDF formátumban megtekinthető és letölthető.
                            </p>
                            <div class="btn-group-vertical">
                                <a href="{{ pdf_url }}" class="btn btn-light btn-lg mb-3" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> PDF megtekintése új ablakban
                                </a>
                                <a href="{{ pdf_url }}" class="btn btn-outline-light btn-lg" download>
                                    <i class="fas fa-download"></i> PDF letöltése
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% elif conversion_status == "pdf_original" and pdf_url %}
                <!-- Original PDF File Card -->
                <div class="pdf-success-card">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">
                                <i class="fas fa-file-pdf fa-2x mb-3"></i><br>
                                PDF fájl készen áll!
                            </h4>
                            <p class="card-text mb-4">
                                A PDF fájl közvetlenül megtekinthető és letölthető.
                            </p>
                            <div class="btn-group-vertical">
                                <a href="{{ pdf_url }}" class="btn btn-light btn-lg mb-3" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> PDF megtekintése új ablakban
                                </a>
                                <a href="{{ pdf_url }}" class="btn btn-outline-light btn-lg" download>
                                    <i class="fas fa-download"></i> PDF letöltése
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% elif conversion_status == "failed" %}
                <!-- Conversion Failed -->
                <div class="conversion-error">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> Konvertálási hiba</h5>
                        <p>A PowerPoint fájl nem konvertálható PDF formátumba. Kérjük, töltse le az eredeti fájlt.</p>
                        <a href="{{ file_url }}" class="btn btn-primary" download>
                            <i class="fas fa-download"></i> PowerPoint letöltése
                        </a>
                    </div>
                </div>
            {% endif %}

            <!-- Announcement Content -->
            <div class="content-card mt-4">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <h5 class="card-title">Tartalom</h5>
                        <div class="content">
                            {{ announcement.content|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.file-info-card {
    animation: slideUp 0.6s ease-out;
}

.content-card {
    animation: slideUp 0.8s ease-out;
}

.pdf-success-card {
    animation: slideUp 0.6s ease-out;
    margin-bottom: 20px;
}

.pdf-success-card .card {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.pdf-success-card .btn-group-vertical .btn {
    margin-bottom: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pdf-success-card .btn-group-vertical .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.conversion-error {
    animation: slideUp 0.8s ease-out;
}

.content {
    line-height: 1.6;
    font-size: 1.1em;
    white-space: pre-wrap;
}

.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .btn-group .btn {
        margin-right: 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .file-info-card .row {
        text-align: center;
    }

    .file-info-card .col-auto {
        margin-bottom: 10px;
    }
}
</style>

{% endblock %}
