#!/bin/bash

# Exit on any error
set -e

# Change to the info directory where manage.py is located
cd /app/info

# Using SQLite - no need to wait for database connection
echo "Using SQLite database - no connection check needed"

# Run database migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Note: Admin user creation removed - create manually using:
# docker-compose exec web python manage.py createsuperuser
echo "Admin user creation skipped - create manually if needed"

# Execute the main command
echo "Starting application..."
exec "$@"
