version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: info_portal
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Django Web Application
  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 --workers 3 info.wsgi:application
    volumes:
      - ./info:/app/info
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=your-secret-key-here-change-in-production
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=info_portal
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - ALLOWED_HOSTS=*
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - static_volume:/app/staticfiles:ro
      - media_volume:/app/media:ro
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
