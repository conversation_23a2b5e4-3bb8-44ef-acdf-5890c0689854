# Információs Portál - Docker Setup

Ez a Django-alapú információs portál Docker konténerekben futtatható.

## 🚀 Gyors indítás

### Fejlesztési környezet (SQLite)

```bash
# Egyszerű fejlesztési verzió SQLite adatbázissal
docker-compose -f docker-compose.dev.yml up --build
```

Az alkalmazás elérhető lesz: http://localhost:8000

### Produk<PERSON><PERSON> környezet (PostgreSQL + Nginx)

```bash
# Teljes produkciós stack PostgreSQL adatbázissal és Nginx reverse proxy-val
docker-compose up --build
```

Az alkalmazás elérhető lesz: http://localhost

## 📋 Előfeltételek

- Docker
- Docker Compose

## 🔧 Konfigurációs lehetőségek

### Környezeti változók

A `docker-compose.yml` fájlban módosíthatod a következő változókat:

```yaml
environment:
  - DEBUG=False                    # Debug mód ki/be
  - SECRET_KEY=your-secret-key     # Django titkos kulcs
  - DB_HOST=db                     # Adatbázis host
  - DB_NAME=info_portal           # Adatbázis név
  - DB_USER=postgres              # Adatbázis felhasználó
  - DB_PASSWORD=postgres123       # Adatbázis jelszó
  - ALLOWED_HOSTS=localhost,127.0.0.1  # Engedélyezett hostok
```

### Admin felhasználó létrehozása

Az admin felhasználót manuálisan kell létrehozni:

```bash
# Miközben a konténer fut, nyiss egy új terminált és futtasd:
docker-compose exec web python manage.py createsuperuser

# Kövesd az utasításokat a felhasználónév, email és jelszó megadásához
```

## 🌐 Hálózati hozzáférés

Az alkalmazás elérhető más eszközökről ugyanazon a hálózaton:

### IP cím megkeresése:
```bash
# Windows-on:
ipconfig

# Linux/Mac-en:
ip addr show
```

### Hozzáférés más eszközökről:
- **Fejlesztési mód:** `http://YOUR_IP_ADDRESS:8000`
- **Produkciós mód:** `http://YOUR_IP_ADDRESS`

Részletes útmutató: [NETWORK_ACCESS.md](NETWORK_ACCESS.md)

## 📁 Könyvtár struktúra

```
.
├── Dockerfile              # Docker image definíció
├── docker-compose.yml      # Produkciós stack
├── docker-compose.dev.yml  # Fejlesztési stack
├── requirements.txt        # Python függőségek
├── entrypoint.sh           # Konténer indítási script
├── nginx.conf              # Nginx konfiguráció
├── .dockerignore           # Docker ignore fájl
└── info/                   # Django projekt
    ├── manage.py
    ├── info/               # Django settings
    └── info_board/         # Django app
```

## 🛠️ Hasznos Docker parancsok

### Konténerek kezelése

```bash
# Konténerek indítása háttérben
docker-compose up -d

# Konténerek leállítása
docker-compose down

# Konténerek újraépítése
docker-compose up --build

# Logok megtekintése
docker-compose logs -f web

# Django parancsok futtatása
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser
docker-compose exec web python manage.py collectstatic
```

### Adatbázis kezelés

```bash
# PostgreSQL shell elérése
docker-compose exec db psql -U postgres -d info_portal

# Adatbázis backup
docker-compose exec db pg_dump -U postgres info_portal > backup.sql

# Adatbázis restore
docker-compose exec -T db psql -U postgres info_portal < backup.sql
```

### Fejlesztés

```bash
# Fejlesztési környezet indítása
docker-compose -f docker-compose.dev.yml up

# Django shell elérése
docker-compose exec web python manage.py shell

# Új migrációk létrehozása
docker-compose exec web python manage.py makemigrations
```

## 🔒 Biztonsági megjegyzések

### Produkciós környezethez

1. **SECRET_KEY módosítása:** Generálj új Django secret key-t
2. **Adatbázis jelszó:** Változtasd meg az alapértelmezett jelszót
3. **Admin jelszó:** Változtasd meg az alapértelmezett admin jelszót
4. **ALLOWED_HOSTS:** Állítsd be a megfelelő domain neveket
5. **SSL/HTTPS:** Használj HTTPS-t produkciós környezetben

### Példa produkciós környezeti változók

```bash
# .env fájl létrehozása
SECRET_KEY=your-very-long-and-random-secret-key-here
DEBUG=False
DB_PASSWORD=your-secure-database-password
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

## 📊 Monitorozás

### Logok

```bash
# Összes szolgáltatás logjai
docker-compose logs -f

# Csak a web alkalmazás logjai
docker-compose logs -f web

# Csak az adatbázis logjai
docker-compose logs -f db
```

### Állapot ellenőrzés

```bash
# Futó konténerek listája
docker-compose ps

# Erőforrás használat
docker stats
```

## 🚨 Hibaelhárítás

### Gyakori problémák

1. **Port foglalt:** Ha a 8000 vagy 80-as port foglalt, módosítsd a `docker-compose.yml`-ben
2. **Adatbázis kapcsolat:** Várj, amíg a PostgreSQL konténer teljesen elindul
3. **Statikus fájlok:** Futtasd a `collectstatic` parancsot
4. **Migrációk:** Ellenőrizd, hogy a migrációk lefutottak-e

### Teljes újraindítás

```bash
# Minden leállítása és törlése
docker-compose down -v
docker system prune -f

# Újraépítés és indítás
docker-compose up --build
```

## 📝 Fejlesztési jegyzetek

- A fejlesztési környezetben a kód változások automatikusan érvényesülnek
- A produkciós környezetben Gunicorn WSGI szerver fut
- Nginx reverse proxy kezeli a statikus fájlokat és load balancing-et
- PostgreSQL adatbázis perzisztens volume-ban tárolja az adatokat
