# Network Access Guide

## Accessing the Application from Different Devices

Your Django application is now configured to accept connections from any device on your local network.

### 🌐 Finding Your Server's IP Address

#### On Windows:
```cmd
ipconfig
```
Look for your network adapter's IPv4 address (usually starts with 192.168.x.x or 10.x.x.x)

#### On Linux/Mac:
```bash
ip addr show
# or
ifconfig
```

### 📱 Accessing from Other Devices

Once you know your server's IP address (e.g., *************), other devices can access:

#### Development Mode:
```
http://*************:8000
```

#### Production Mode (with Nginx):
```
http://*************
```

### 🔧 Setup Instructions

1. **Start the application:**
   ```bash
   # Development mode
   docker-compose -f docker-compose.dev.yml up

   # Production mode
   docker-compose up
   ```

2. **Find your IP address** using the commands above

3. **Access from any device** on the same network using your IP address

### 📋 Creating Admin User

Since automatic admin creation is disabled, create an admin user manually:

```bash
# While the container is running, open a new terminal and run:
docker-compose exec web python manage.py createsuperuser

# Follow the prompts to create username, email, and password
```

### 🔒 Firewall Considerations

If you can't access from other devices, check:

#### Windows Firewall:
- Allow Docker Desktop through Windows Firewall
- Allow ports 8000 (development) or 80 (production)

#### Router/Network:
- Ensure devices are on the same network/subnet
- Some corporate networks may block inter-device communication

### 📱 Mobile Access

Perfect for testing on mobile devices:
- Open browser on phone/tablet
- Navigate to `http://YOUR_IP_ADDRESS:8000`
- The responsive design will adapt to mobile screens

### 🛠️ Troubleshooting

#### Can't access from other devices?
1. Check firewall settings
2. Verify IP address is correct
3. Ensure containers are running: `docker-compose ps`
4. Check if port is accessible: `telnet YOUR_IP 8000`

#### Application not loading?
1. Check container logs: `docker-compose logs web`
2. Verify ALLOWED_HOSTS is set to `*`
3. Restart containers: `docker-compose restart`

### 🔧 Advanced Configuration

For production environments, you may want to:
1. Set specific IP addresses in ALLOWED_HOSTS instead of `*`
2. Configure SSL/HTTPS for secure access
3. Set up a proper domain name
4. Configure firewall rules for specific devices only

Example for specific hosts:
```yaml
environment:
  - ALLOWED_HOSTS=*************,*************,yourdomain.com
```
