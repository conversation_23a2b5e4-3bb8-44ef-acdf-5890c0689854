@echo off
REM InfoTable Application Service Uninstaller

echo ========================================
echo InfoTable Service Uninstaller
echo ========================================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

set SERVICE_NAME=InfoTableApp
set APP_DIR=%~dp0

REM Stop the application first
echo Stopping InfoTable application...
call "%APP_DIR%stop-app.bat"

REM Check if service exists
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo Service %SERVICE_NAME% does not exist.
    pause
    exit /b 0
)

REM Stop and remove the service
echo Stopping service...
"%APP_DIR%nssm.exe" stop %SERVICE_NAME%

echo Removing service...
"%APP_DIR%nssm.exe" remove %SERVICE_NAME% confirm

echo ========================================
echo Service uninstallation completed!
echo ========================================

pause
