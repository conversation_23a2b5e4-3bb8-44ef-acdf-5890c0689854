from django.contrib import admin
from django.utils.html import format_html
from .models import Announcement


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ('title', 'date_posted', 'is_active',
                    'has_image', 'has_attachment')
    list_filter = ('is_active', 'date_posted')
    search_fields = ('title', 'content')
    readonly_fields = ('date_posted',)

    def has_image(self, obj):
        """Display whether the announcement has an associated image."""
        if obj.background_image:
            return format_html(
                '<span style="color: green;">✓ Igen</span>'
            )
        return format_html(
            '<span style="color: red;">✗ Nem</span>'
        )
    has_image.short_description = 'Kép csatolva'

    def has_attachment(self, obj):
        """Display whether the announcement has an attached PowerPoint file."""
        if obj.attachment_file:
            file_name = obj.attachment_file.name.split('/')[-1]
            return format_html(
                '<span style="color: green;">✓ {}</span>',
                file_name
            )
        return format_html(
            '<span style="color: red;">✗ Nem</span>'
        )
    has_attachment.short_description = 'Melléklet csatolva'

    def delete_model(self, request, obj):
        """Override delete_model to provide feedback about image deletion."""
        image_path = None
        if obj.background_image:
            image_path = obj.background_image.path

        # Call the parent delete method (this will trigger our signals)
        super().delete_model(request, obj)

        # Provide feedback message
        if image_path:
            self.message_user(
                request,
                f'A "{obj.title}" közlemény és a hozzá tartozó kép sikeresen törölve.',
                level='SUCCESS'
            )
        else:
            self.message_user(
                request,
                f'A "{obj.title}" közlemény sikeresen törölve.',
                level='SUCCESS'
            )

    def delete_queryset(self, request, queryset):
        """Override delete_queryset for bulk deletions."""
        # Count images that will be deleted
        images_count = queryset.filter(background_image__isnull=False).count()
        total_count = queryset.count()

        # Call the parent delete method (this will trigger our signals)
        super().delete_queryset(request, queryset)

        # Provide feedback message
        if images_count > 0:
            self.message_user(
                request,
                f'{total_count} közlemény és {images_count} hozzájuk tartozó kép sikeresen törölve.',
                level='SUCCESS'
            )
        else:
            self.message_user(
                request,
                f'{total_count} közlemény sikeresen törölve.',
                level='SUCCESS'
            )
