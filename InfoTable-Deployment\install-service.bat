@echo off
REM InfoTable Application Service Installer
REM This script installs the InfoTable application as a Windows service

echo ========================================
echo InfoTable Service Installer
echo ========================================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=InfoTableApp
set APP_DIR=%~dp0
set DOCKER_COMPOSE_FILE=%APP_DIR%docker-compose.prod.yml

echo Current directory: %APP_DIR%
echo Docker Compose file: %DOCKER_COMPOSE_FILE%

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Docker is not installed or not in PATH!
    echo Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose file exists
if not exist "%DOCKER_COMPOSE_FILE%" (
    echo ERROR: Docker Compose file not found: %DOCKER_COMPOSE_FILE%
    pause
    exit /b 1
)

REM Download NSSM if not exists
if not exist "%APP_DIR%nssm.exe" (
    echo Downloading NSSM (Non-Sucking Service Manager)...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nssm.cc/release/nssm-2.24.zip' -OutFile '%APP_DIR%nssm.zip'}"
    powershell -Command "& {Expand-Archive -Path '%APP_DIR%nssm.zip' -DestinationPath '%APP_DIR%' -Force}"
    copy "%APP_DIR%nssm-2.24\win64\nssm.exe" "%APP_DIR%nssm.exe"
    rmdir /s /q "%APP_DIR%nssm-2.24"
    del "%APP_DIR%nssm.zip"
)

REM Stop and remove existing service if it exists
echo Checking for existing service...
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    "%APP_DIR%nssm.exe" stop %SERVICE_NAME%
    echo Removing existing service...
    "%APP_DIR%nssm.exe" remove %SERVICE_NAME% confirm
)

REM Create the service
echo Creating service: %SERVICE_NAME%
"%APP_DIR%nssm.exe" install %SERVICE_NAME% "%APP_DIR%start-app.bat"

REM Configure service
echo Configuring service...
"%APP_DIR%nssm.exe" set %SERVICE_NAME% AppDirectory "%APP_DIR%"
"%APP_DIR%nssm.exe" set %SERVICE_NAME% DisplayName "InfoTable Application"
"%APP_DIR%nssm.exe" set %SERVICE_NAME% Description "InfoTable Django Application running in Docker"
"%APP_DIR%nssm.exe" set %SERVICE_NAME% Start SERVICE_AUTO_START
"%APP_DIR%nssm.exe" set %SERVICE_NAME% AppStdout "%APP_DIR%logs\service.log"
"%APP_DIR%nssm.exe" set %SERVICE_NAME% AppStderr "%APP_DIR%logs\service-error.log"

REM Create logs directory
if not exist "%APP_DIR%logs" mkdir "%APP_DIR%logs"

REM Start the service
echo Starting service...
"%APP_DIR%nssm.exe" start %SERVICE_NAME%

echo ========================================
echo Service installation completed!
echo ========================================
echo Service Name: %SERVICE_NAME%
echo Status: Use 'services.msc' to check service status
echo Logs: %APP_DIR%logs\
echo ========================================

pause
