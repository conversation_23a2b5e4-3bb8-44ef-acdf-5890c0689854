from django.db import models
from django.db.models.signals import pre_delete, post_delete, pre_save
from django.dispatch import receiver
import os


def validate_ppt_file(value):
    """Validate that the uploaded file is a PowerPoint file."""
    if value:
        ext = os.path.splitext(value.name)[1].lower()
        if ext not in ['.ppt', '.pptx']:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                'Only PowerPoint files (.ppt, .pptx) are allowed.')


class Announcement(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    date_posted = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    background_image = models.ImageField(
        upload_to='announcements/', null=True, blank=True)
    attachment_file = models.FileField(
        upload_to='announcements/attachments/',
        null=True,
        blank=True,
        validators=[validate_ppt_file],
        help_text='Upload PowerPoint files (.ppt, .pptx) only'
    )

    def __str__(self):
        return self.title

    def has_powerpoint_attachment(self):
        """Check if the announcement has a PowerPoint attachment."""
        if self.attachment_file:
            ext = os.path.splitext(self.attachment_file.name)[1].lower()
            return ext in ['.ppt', '.pptx']
        return False

    def get_file_extension(self):
        """Get the file extension of the attachment."""
        if self.attachment_file:
            return os.path.splitext(self.attachment_file.name)[1].lower()
        return None

    def get_pdf_path(self):
        """Get the PDF version of the PowerPoint attachment."""
        from .utils import get_pdf_path_for_announcement
        return get_pdf_path_for_announcement(self)

    def get_pdf_url(self, request):
        """Get the full URL for the PDF version."""
        from .utils import get_pdf_url_for_announcement
        return get_pdf_url_for_announcement(self, request)


class EmailMessage(models.Model):
    sender_email = models.EmailField()
    recipient_email = models.EmailField()
    content = models.TextField()


# Signal handlers for automatic file deletion
@receiver(pre_delete, sender=Announcement)
def delete_announcement_files(sender, instance, **kwargs):
    """
    Delete the associated files when an Announcement is deleted.
    This signal is triggered before the model instance is deleted from the database.
    """
    # Delete background image
    if instance.background_image:
        if os.path.isfile(instance.background_image.path):
            try:
                os.remove(instance.background_image.path)
                print(f"Deleted image file: {instance.background_image.path}")
            except OSError as e:
                print(
                    f"Error deleting image file {instance.background_image.path}: {e}")

    # Delete attachment file
    if instance.attachment_file:
        if os.path.isfile(instance.attachment_file.path):
            try:
                os.remove(instance.attachment_file.path)
                print(
                    f"Deleted attachment file: {instance.attachment_file.path}")
            except OSError as e:
                print(
                    f"Error deleting attachment file {instance.attachment_file.path}: {e}")

        # Also delete the corresponding PDF file
        try:
            from django.conf import settings
            pdf_path = instance.get_pdf_path()
            if pdf_path:
                full_pdf_path = os.path.join(settings.MEDIA_ROOT, pdf_path)
                if os.path.isfile(full_pdf_path):
                    os.remove(full_pdf_path)
                    print(f"Deleted PDF file: {full_pdf_path}")
        except Exception as e:
            print(f"Error deleting PDF file: {e}")


@receiver(post_delete, sender=Announcement)
def cleanup_empty_directories(sender, instance, **kwargs):
    """
    Clean up empty directories after deleting an announcement.
    This helps keep the media directory organized.
    """
    if instance.background_image:
        try:
            # Get the directory path
            directory = os.path.dirname(instance.background_image.path)
            # Remove directory if it's empty
            if os.path.exists(directory) and not os.listdir(directory):
                os.rmdir(directory)
                print(f"Removed empty directory: {directory}")
        except OSError as e:
            print(f"Error removing directory: {e}")


@receiver(pre_save, sender=Announcement)
def delete_old_files_on_update(sender, instance, **kwargs):
    """
    Delete the old files when an Announcement's files are updated.
    This prevents orphaned files when files are replaced.
    """
    if not instance.pk:
        # This is a new instance, no old files to delete
        return

    try:
        # Get the old instance from the database
        old_instance = Announcement.objects.get(pk=instance.pk)

        # Check if the image field has changed
        if old_instance.background_image and old_instance.background_image != instance.background_image:
            # Delete the old image file
            if os.path.isfile(old_instance.background_image.path):
                try:
                    os.remove(old_instance.background_image.path)
                    print(
                        f"Deleted old image file: {old_instance.background_image.path}")
                except OSError as e:
                    print(
                        f"Error deleting old image file {old_instance.background_image.path}: {e}")

        # Check if the attachment field has changed
        if old_instance.attachment_file and old_instance.attachment_file != instance.attachment_file:
            # Delete the old attachment file
            if os.path.isfile(old_instance.attachment_file.path):
                try:
                    os.remove(old_instance.attachment_file.path)
                    print(
                        f"Deleted old attachment file: {old_instance.attachment_file.path}")
                except OSError as e:
                    print(
                        f"Error deleting old attachment file {old_instance.attachment_file.path}: {e}")
    except Announcement.DoesNotExist:
        # Old instance doesn't exist, this is a new record
        pass
