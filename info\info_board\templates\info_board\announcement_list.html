{% extends 'info_board/base.html' %}

{% block title %}Közlemények{% endblock %}

{% block content %}
<div class="container">
    <!-- Cards View -->
    <div id="cards-view">
        <h1 class="mb-4">Információk, programok</h1>
        <div class="row row-cols-1 row-cols-md-3 g-4">
            {% for announcement in announcements %}
                <div class="col">
                    <div class="card h-100 announcement-card" style="cursor: pointer;" onclick="showDetail({{ announcement.id }})">
                        {% if announcement.background_image %}
                            <img src="{{ announcement.background_image.url }}" class="card-img-top" alt="{{ announcement.title }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="card-img-top bg-secondary" style="height: 200px;"></div>
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ announcement.title }}</h5>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col">
                    <p>Je<PERSON><PERSON> nincsenek elérhető információk.</p>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- Detail View -->
    <div id="detail-view" class="detail-container" style="display: none;">
        <button class="btn btn-outline-light back-button" onclick="showCards()">
            <i class="fas fa-arrow-left"></i> Vissza
        </button>
        <div class="detail-content">
            <div id="detail-image-container"></div>
            <h2 id="detail-title" class="mb-3"></h2>
            <p id="detail-date" class="mb-4" style="color: rgba(255, 255, 255, 0.8) !important; font-weight: 500;"></p>
            <div id="detail-content" class="content"></div>
        </div>
    </div>

    <!-- Hidden data for JavaScript -->
    <script type="application/json" id="announcements-data">
        [
            {% for announcement in announcements %}
            {
                "id": {{ announcement.id }},
                "title": "{{ announcement.title|escapejs }}",
                "content": "{{ announcement.content|escapejs }}",
                "date_posted": "{{ announcement.date_posted|date:'Y. F j.' }}",
                "background_image": {% if announcement.background_image %}"{{ announcement.background_image.url }}"{% else %}null{% endif %}
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    </script>
</div>

<style>
    /* Enhanced Card Animations */
    .announcement-card {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid transparent;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        overflow: hidden;
        position: relative;
    }

    .announcement-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(40, 167, 69, 0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .announcement-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(0, 123, 255, 0.3),
            0 0 20px rgba(0, 123, 255, 0.2),
            inset 0 0 0 2px rgba(0, 123, 255, 0.4);
        border-color: rgba(0, 123, 255, 0.6);
    }

    .announcement-card:hover::before {
        opacity: 1;
    }

    .announcement-card:active {
        transform: translateY(-8px) scale(1.01);
        transition: all 0.1s ease;
    }

    .announcement-card .card-body {
        position: relative;
        z-index: 2;
        color: #333;
    }

    .announcement-card .card-img-top {
        transition: transform 0.4s ease;
    }

    .announcement-card:hover .card-img-top {
        transform: scale(1.05);
    }

    /* Detail View Styles */
    .detail-container {
        animation: fadeIn 0.5s ease-in-out;
        min-height: 80vh;
    }

    .back-button {
        position: fixed;
        top: 80px;
        left: 20px;
        z-index: 1000;
        border-radius: 50px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s ease;
        background: rgba(0, 123, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(0, 123, 255, 0.4);
    }

    .back-button:hover {
        background: rgba(0, 123, 255, 0.4);
        transform: translateX(-5px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }

    .detail-content {
        max-width: 800px;
        margin: 60px auto 0;
        padding: 40px;
        background: rgba(51, 51, 51, 0.9);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: slideUp 0.6s ease-out;
    }

    .detail-content img {
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        margin-bottom: 20px;
    }

    .content {
        text-wrap: wrap;
        word-wrap: break-word;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        line-height: 1.6;
        font-size: 1.1em;
    }

    /* Animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced mobile responsiveness */
    @media (max-width: 768px) {
        .back-button {
            top: 70px;
            left: 10px;
            padding: 10px 18px;
            font-size: 0.95em;
            min-height: 44px; /* Touch-friendly */
        }

        .detail-content {
            margin: 50px 10px 0;
            padding: 20px 15px;
        }

        .announcement-card:hover {
            transform: translateY(-5px) scale(1.01);
        }

        /* Better card layout on mobile */
        .row-cols-1 {
            --bs-gutter-x: 1rem;
        }

        .announcement-card {
            margin-bottom: 1rem;
        }

        .card-img-top {
            height: 180px !important;
        }

        .card-title {
            font-size: 1.1rem;
            line-height: 1.3;
        }

        /* Touch improvements */
        .announcement-card {
            cursor: pointer;
            -webkit-tap-highlight-color: rgba(0, 123, 255, 0.2);
        }
    }

    @media (max-width: 576px) {
        .back-button {
            top: 65px;
            left: 5px;
            padding: 8px 15px;
            font-size: 0.9em;
        }

        .detail-content {
            margin: 45px 5px 0;
            padding: 15px 10px;
        }

        .card-img-top {
            height: 160px !important;
        }

        .card-title {
            font-size: 1rem;
        }

        h1 {
            font-size: 1.5rem;
        }

        .content {
            font-size: 1rem;
            line-height: 1.5;
        }
    }
</style>

<script>
    // Get announcements data
    const announcementsData = JSON.parse(document.getElementById('announcements-data').textContent);

    function showDetail(announcementId) {
        // Find the announcement data
        const announcement = announcementsData.find(a => a.id === announcementId);
        if (!announcement) return;

        // Hide cards view with animation
        const cardsView = document.getElementById('cards-view');
        cardsView.style.opacity = '0';
        cardsView.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            cardsView.style.display = 'none';

            // Populate detail view
            document.getElementById('detail-title').textContent = announcement.title;
            document.getElementById('detail-date').textContent = 'Közzétéve: ' + announcement.date_posted;
            document.getElementById('detail-content').innerHTML = announcement.content.replace(/\n/g, '<br>');

            // Handle image
            const imageContainer = document.getElementById('detail-image-container');
            if (announcement.background_image) {
                imageContainer.innerHTML = `<img src="${announcement.background_image}" class="img-fluid mb-3" alt="${announcement.title}">`;
            } else {
                imageContainer.innerHTML = '';
            }

            // Show detail view
            const detailView = document.getElementById('detail-view');
            detailView.style.display = 'block';
            detailView.style.opacity = '0';

            // Trigger animation
            setTimeout(() => {
                detailView.style.opacity = '1';
            }, 50);

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 300);
    }

    function showCards() {
        // Hide detail view with animation
        const detailView = document.getElementById('detail-view');
        detailView.style.opacity = '0';

        setTimeout(() => {
            detailView.style.display = 'none';

            // Show cards view
            const cardsView = document.getElementById('cards-view');
            cardsView.style.display = 'block';
            cardsView.style.opacity = '0';
            cardsView.style.transform = 'translateY(20px)';

            // Trigger animation
            setTimeout(() => {
                cardsView.style.opacity = '1';
                cardsView.style.transform = 'translateY(0)';
            }, 50);

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 300);
    }

    // Add smooth transitions to cards view
    document.addEventListener('DOMContentLoaded', function() {
        const cardsView = document.getElementById('cards-view');
        cardsView.style.transition = 'all 0.3s ease-in-out';
    });
</script>
{% endblock %}
