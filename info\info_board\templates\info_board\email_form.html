{% extends 'info_board/base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card" style="background-color: rgba(51, 51, 51, 0.7); backdrop-filter: blur(10px);">
                <div class="card-body text-white">
                    <h2 class="card-title mb-4"><PERSON><PERSON></h2>
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label class="form-label">{{ form.sender_name.label }}</label>
                            {{ form.sender_name.errors }}
                            <input type="text" name="sender_name" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.sender_email.label }}</label>
                            {{ form.sender_email.errors }}
                            <input type="email" name="sender_email" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.recipient_email.label }}</label>
                            {{ form.recipient_email.errors }}
                            <input type="email" name="recipient_email" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.content.label }}</label>
                            {{ form.content.errors }}
                            <textarea name="content" class="form-control bg-dark text-white" style="min-height: 200px;" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary px-4 py-2">Küldés</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control {
        border: 1px solid #444;
        background-color: rgba(33, 33, 33, 0.7) !important;
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px; /* Touch-friendly */
    }
    .form-control:focus {
        background-color: rgba(51, 51, 51, 0.9) !important;
        color: white;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Mobile optimizations for email form */
    @media (max-width: 768px) {
        .col-md-8 {
            padding: 0 10px;
        }

        .card {
            margin: 10px 0;
            border-radius: 12px;
        }

        .card-body {
            padding: 20px 15px;
        }

        .form-control {
            padding: 12px 15px;
            border-radius: 8px;
        }

        textarea.form-control {
            min-height: 150px !important;
            resize: vertical;
        }

        .btn {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border-radius: 8px;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }
    }

    @media (max-width: 576px) {
        .container {
            padding: 0 5px;
        }

        .card-body {
            padding: 15px 10px;
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        textarea.form-control {
            min-height: 120px !important;
        }
    }
</style>
{% endblock %}