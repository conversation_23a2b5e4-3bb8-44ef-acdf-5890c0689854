@echo off
REM Docker Desktop Setup for InfoTable Application

echo ========================================
echo Docker Desktop Setup for InfoTable
echo ========================================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Configuring Docker Desktop for automatic startup...

REM Enable Docker Desktop to start with Windows
reg add "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run" /v "Docker Desktop" /t REG_SZ /d "C:\Program Files\Docker\Docker\Docker Desktop.exe" /f

REM Configure Docker to start automatically
echo Configuring Docker service to start automatically...
sc config "com.docker.service" start= auto

echo ========================================
echo Docker Desktop configuration completed!
echo ========================================
echo.
echo Docker Desktop will now start automatically with Windows.
echo Please restart your computer to ensure all settings take effect.
echo.
echo After restart, run install-service.bat to install the InfoTable service.
echo ========================================

pause
